import { defineConfig } from 'vitest/config';
import * as fs from 'fs';

// Ensure HAR recordings directory exists
const harDir = './e2e/recordings';
if (!fs.existsSync(harDir)) {
  fs.mkdirSync(harDir, { recursive: true });
}

// Ensure screenshots directory exists
const screenshotsDir = './e2e/screenshots';
if (!fs.existsSync(screenshotsDir)) {
  fs.mkdirSync(screenshotsDir, { recursive: true });
}

const isCI = process.env.CI === 'true';

export default defineConfig({
  test: {
    environment: 'node',
    globals: true,
    include: ['e2e/**/*.e2e.ts'],
    exclude: ['tests/**/*'],
    testTimeout: 10000, // Increased timeout since each test starts its own Electron instance
    hookTimeout: 10000, // Increased timeout for setup/teardown
    fileParallelism: true,
    minWorkers: 4, // Reduced to avoid overwhelming system with too many Electron instances
    maxConcurrency: 8, // Increased for better parallelization with per-test isolation
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        isolate: true
      }
    },
    reporter: isCI ? ['verbose', 'junit'] : 'default',
    outputFile: isCI ? {
      junit: './test-results/junit.xml'
    } : undefined,
    globalSetup: './e2e/global-setup.ts',
    globalTeardown: './e2e/global-teardown.ts',
    // Ensure screenshots and debug directories are preserved
    setupFiles: ['./e2e/test-setup.ts']
  },
  define: {
    global: 'globalThis'
  }
});
